战斗场 = { 15, 59, 50, 76, "99561f-101010",
	"-1|0|99561f-101010|-2|1|99561f-101010|-3|2|99561f-101010|-4|2|99561f-101010|12|-7|a87d39-101010|13|-5|b97d45-101010|-11|6|a26733-101010|-12|5|a26733-101010|-8|7|a77d39-101010" }
自动 = { 1204, 644, 1231, 672, "fafaff-101010",
	"0|4|f9fcff-101010|-2|8|aaccfa-101010|-9|6|faf7ff-101010|-9|2|fcfeff-101010|2|2|409cf2-101010|2|4|3592f2-101010|-4|0|a6cbf9-101010|-3|5|fcfcfe-101010|-5|6|faf9ff-101010" }
取消 = { 1191, 636, 1225, 670, "ffffff-101010",
	"3|-8|ffffff-101010|1|-7|6db6f8-101010|1|-6|61b1f1-101010|-7|-5|ffffff-101010|-9|-5|f4f8fd-101010|-9|-7|1c8deb-101010|-8|-6|a3d0f6-101010|-11|-9|fdfeff-101010|-12|-8|f6fbfd-101010|-9|5|eef0fe-101010|-9|6|b4def4-101010|-9|7|71c1f0-101010" }
召唤 = { 929, 633, 982, 680, "ffffff-101010",
	"8|0|ffffff-101010|13|-7|4784d4-101010|16|-10|549be3-101010|14|-9|4888d5-101010|-2|-12|0c0e2d-101010|-2|-10|090c33-101010|-12|-12|427bc6-101010|-11|-19|448ed8-101010|-11|-2|f8ffff-101010|22|10|2a43a5-101010" }
复活丹 = { 308, 210, 973, 524, "c05019-101010",
	"-13|1|fca462-101010|-19|-3|f99454-101010|-25|-7|f48446-101010|-12|-29|aac314-101010|-17|-25|aac314-101010|-9|-31|aac314-101010|16|-28|94b619-101010|13|-26|94b619-101010|15|-37|fbf168-101010|12|-34|e4f134-101010" }
已选定 = { 919, 638, 987, 674, "fefefe-101010",
	"0|5|fefefe-101010|-6|8|ffffff-101010|-14|8|ffffff-101010|-20|10|fefefe-101010|11|6|fefefe-101010|11|11|fefefe-101010|24|0|ffffff-101010|26|7|fefefe-101010|29|15|fefefe-101010" }
背包面板 = { 591, 55, 689, 101, "b1cdf3-101010",
	"1|11|99bbed-101010|-4|10|9cbdef-101010|-13|9|ffffff-101010|-19|5|ffffff-101010|-22|2|ffffff-101010|-25|-4|ffffff-101010|26|-1|ffffff-101010|21|2|ffffff-101010|27|-7|ffffff-101010|38|1|ffffff-101010" }
购买加号 = { 313, 220, 408, 301, "81e136-101010",
	"4|-24|85e537-101010|1|-34|bcfe4d-101010|-7|-2|82e236-101010|-16|-2|82e236-101010|-27|-5|87e838-101010|6|4|81e135-101010|27|4|81e135-101010|-4|26|7cda33-101010" }
药品商店 = { 567, 33, 641, 72, "4554ce-101010",
	"2|3|4257d0-101010|-4|4|ffffff-101010|-8|3|fefefe-101010|-13|2|ffffff-101010|-10|-7|ffffff-101010|16|-3|4951d0-101010|17|-4|4a51d1-101010|23|-3|ffffff-101010|22|1|ffffff-101010|19|10|ffffff-101010|14|12|ffffff-101010|4|13|ffffff-101010" }
面板叉 = { 1127, 19, 1169, 61, "1c4cc5-101010",
	"-3|-3|1c4cc5-101010|-5|-5|1c4bc5-101010|-1|-6|bcaffe-101010|0|-4|bdb1fe-101010|7|-4|2d56cb-101010|3|-2|1c4cc5-101010|7|2|c9c0ff-101010|7|7|204fc6-101010|0|8|c9befd-101010" }
难度 = { 73, 101, 711, 548, "00b1fe-101010",
	"-3|1|00b1ff-101010|-5|1|00b1ff-101010|5|3|00b0fe-101010|-4|5|00b1ff-101010|-9|6|0bb1fa-101010|-4|11|01b1ff-101010|5|11|01b1ff-101010|-1|16|00a9f3-101010|-4|18|01b1ff-101010|5|18|00b0fe-101010|7|18|08b0fa-101010" }



function 多点找色(数据, 点击, 时间)
	local 成功, 结果 = pcall(function()
		if not 数据 then
			error("数据参数为空")
		end

		local x, y = findMultiColor(数据[1], 数据[2], 数据[3], 数据[4], 数据[5], 数据[6], 0, 0.9)
		if x > -1 then
			if 点击 == 1 then
				tap(x, y)

				sleep(时间)
			end
			return true
		else
			return false
		end
	end)

	if not 成功 then
		local 信息 = debug.getinfo(2)
		local 文件名 = 信息.source:match("([^/\\]+)$") -- 提取文件名
		print(string.format("错误位置: %s 第%d行", 文件名, 信息.currentline))
		return false
	end

	return 结果
end

function 多点找色难度(数据, 点击, 时间)
	local 成功, 结果 = pcall(function()
		if not 数据 then
			error("数据参数为空")
		end

		local x, y = findMultiColor(数据[1], 数据[2], 数据[3], 数据[4], 数据[5], 数据[6], 0, 0.9)
		if x > -1 then
			if 点击 == 1 then
				tap(x, y - 38)

				sleep(时间)
			end
			return true
		else
			return false
		end
	end)




	if not 成功 then
		local 信息 = debug.getinfo(2)
		local 文件名 = 信息.source:match("([^/\\]+)$") -- 提取文件名
		print(string.format("错误位置: %s 第%d行", 文件名, 信息.currentline))
		return false
	end

	return 结果
end
