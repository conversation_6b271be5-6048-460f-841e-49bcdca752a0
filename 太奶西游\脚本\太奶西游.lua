require("太奶函数")
arr = jsonLib.decode(getUIConfig("太奶西游.config"))
--showUI("太奶西游.ui", -2, -2)
local 卡密 = arr["page0"]["输入卡密"]
function 人物技能()
    if 多点找色(召唤, 0, 0) then
        tap(1229, 267) --点法术
        sleep(500)
        if arr["page0"]["技能一"] == "true" then
            print("点第一个技能")
            tap(841, 136) --点第一个技能
            sleep(500)
        elseif arr["page0"]["技能二"] == "true" then
            print("点第二个技能")
            tap(969, 144) --点第二个技能
            sleep(500)
        elseif arr["page0"]["技能三"] == "true" then
            print("点第三个技能")
            tap(1105, 136) --点第三个技能
            sleep(500)
        elseif arr["page0"]["技能四"] == "true" then
            print("点第四个技能")
            tap(837, 262) --点第四个技能
            sleep(500)
        elseif arr["page0"]["技能五"] == "true" then
            print("点第五个技能")
            tap(974, 259) --点第五个技能
            sleep(500)
        elseif arr["page0"]["技能六"] == "true" then
            print("点第六个技能")
            tap(1104, 260) --点第六个技能
            sleep(500)
        end
        多点找色难度(难度, 1, 500)
        return true
    end
    return false
end

function 宝宝技能()
    if 多点找色(自动, 0, 0) then
        tap(1234, 572) --点道具
        sleep(500)
        if 多点找色(背包面板, 0, 0) then
            tap(695, 149) --点背包复活
            sleep(500)
        end
        买复活丹()
        if 多点找色(复活丹, 1, 500) then
            tap(639, 618) --点使用
            sleep(500)
            if arr["page0"]["左一"] == "true" then
                print("点左一")
                tap(765, 568) --点人物坐标
                sleep(500)
            elseif arr["page0"]["左二"] == "true" then
                print("点左二")
                tap(869, 500) --点人物坐标
                sleep(500)
            elseif arr["page0"]["队长"] == "true" then
                print("点队长")
                tap(939, 455) --点人物坐标
                sleep(500)
            elseif arr["page0"]["右一"] == "true" then
                print("点右一")
                tap(1045, 379) --点人物坐标
                sleep(500)
            elseif arr["page0"]["右二"] == "true" then
                print("点右二")
                tap(1137, 317) --点人物坐标
                sleep(500)
            end
        end
    end
    return false
end

function 买复活丹()
    if not 多点找色(复活丹, 0, 0) then
        多点找色(购买加号, 1, 1000)
        if 多点找色(药品商店, 0, 0) then
            print("找到药品商店")
            tap(514, 315)  --点复活丹购买
            sleep(1000)
            tap(851, 629)  --点购买数量
            sleep(1000)
            tap(907, 537)  --点9一次
            sleep(500)
            tap(907, 537)  --点9二次
            sleep(500)
            tap(1014, 540) --点ok
            sleep(500)
            tap(1032, 621) --点购买
            sleep(500)
            多点找色(面板叉, 1, 1000)
            return true
        end
    else
        return false
    end
end

while true do
    sleep(500)
    if 多点找色(战斗场, 0, 0) then
        print("找到战斗场")
        if not 人物技能() then --没有找到人物技能
            宝宝技能()
        end
        if not 多点找色(已选定, 0, 0) then --没有找到已选定
            多点找色(取消, 1, 500)
        else
            tap(960, 447)
            sleep(500)
        end
    else
        print("未找到战斗场")
    end
end
